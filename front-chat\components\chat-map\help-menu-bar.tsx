import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  MapIcon,
  LayersIcon,
  SettingsIcon,
  FilterIcon,
  ChevronRightIcon,
} from "lucide-react";

interface HelpMenuBarProps {
  setInput: (input: string) => void;
  hasMessages: boolean;
}

export const HelpMenuBar = ({ setInput, hasMessages }: HelpMenuBarProps) => {
  const [activePopover, setActivePopover] = useState<string | null>(null);

  const menuItems = [
    {
      id: "search",
      icon: <MapIcon className="w-4 h-4" />,
      title: "장소 검색 및 이동",
      tooltip: "장소 검색 및 이동",
      examples: [
        { label: "위치 이동", command: "웨이버스로 이동해줘" },
        { label: "길찾기", command: "웨이버스에서 평촌역까지 얼마나 걸려?" },
        { label: "현재위치에서 길찾기", command: "내 위치에서 구로디지털단지역까지 얼마나걸려?" },
      ]
    },
    {
      id: "control",
      icon: <SettingsIcon className="w-4 h-4" />,
      title: "지도 제어",
      tooltip: "지도 제어",
      examples: [
        { label: "지도 확대", command: "지도를 확대해줘" },
        { label: "지도 축소", command: "지도를 축소해줘" },
        { label: "위쪽으로 500m 이동", command: "위쪽으로 500m 이동해줘" },
        { label: "항공지도로 변경", command: "배경지도를 항공지도로 변경해줘" }
      ]
    },
    {
      id: "layers",
      icon: <LayersIcon className="w-4 h-4" />,
      title: "레이어 제어",
      tooltip: "레이어 제어",
      examples: [
        { label: "레이어 추가", command: "택지개발사업 레이어를 추가해줘" },
        { label: "단일 스타일 설정", command: "백년가게를 노란색 별모양으로 보여줄래?" },
        { label: "유형별 스타일 설정", command: "서울에 있는 약국만 빨간색으로 표시해줘" },
        { label: "유형별 스타일 설정", command: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?" },
      ]
    },
    {
      id: "analysis",
      icon: <FilterIcon className="w-4 h-4" />,
      title: "데이터 분석",
      tooltip: "데이터 분석",
      examples: [
        { label: "노후화 건물 분석", command: "서울의 노후화된 건물을 보여줘" },
        { label: "레이어 밀도 분석", command: "AI 발생농가 지역의 밀집도를 분석해줘" },
      ]
    },
  ];

  const handleExampleClick = (command: string) => {
    setInput(command);
    setActivePopover(null);
  };

  // 메시지가 없을 때는 표시하지 않음 (Overview가 표시되므로)
  if (!hasMessages) {
    return null;
  }

  return (
    <div className="flex items-center justify-center px-4 py-2 border-b border-border/30 bg-gradient-to-r from-secondary/10 to-secondary/20">
      <div className="flex items-center gap-1 bg-background/80 backdrop-blur-sm rounded-lg px-2 py-1 border border-border/40 shadow-sm">
        {menuItems.map((item) => (
          <Popover
            key={item.id}
            open={activePopover === item.id}
            onOpenChange={(open) => setActivePopover(open ? item.id : null)}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`h-8 w-8 p-0 hover:bg-primary/10 transition-colors ${
                      activePopover === item.id ? 'bg-primary/10 text-primary' : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {item.icon}
                  </Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="text-xs">
                {item.tooltip}
              </TooltipContent>
            </Tooltip>
            <PopoverContent
              className="w-72 p-0 bg-gradient-to-b from-background/95 to-background/98 backdrop-blur-lg border border-border/40 shadow-lg"
              align="center"
              side="bottom"
              sideOffset={8}
            >
              <div className="p-3 space-y-3">
                {/* 헤더 */}
                <div className="border-b border-border/40 pb-2">
                  <div className="flex items-center gap-2">
                    {item.icon}
                    <h4 className="font-semibold text-sm text-foreground">
                      {item.title}
                    </h4>
                  </div>
                  <p className="text-xs text-muted-foreground/80 mt-1">
                    예시를 클릭하면 자동으로 입력됩니다
                  </p>
                </div>

                {/* 예시 목록 */}
                <div className="space-y-1 max-h-60 overflow-y-auto styled-scrollbar">
                  {item.examples.map((example, exampleIndex) => (
                    <Button
                      key={exampleIndex}
                      variant="ghost"
                      className="w-full group justify-between relative overflow-hidden rounded-md px-2 py-2 h-auto"
                      onClick={() => handleExampleClick(example.command)}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                      <div className="relative flex flex-col items-start gap-0.5 text-left flex-1">
                        <span className="text-xs font-medium text-foreground/90 group-hover:text-primary transition-colors">
                          {example.label}
                        </span>
                        <span className="text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors line-clamp-2">
                          {example.command}
                        </span>
                      </div>
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <ChevronRightIcon className="w-3 h-3 text-primary" />
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        ))}
      </div>
    </div>
  );
};
