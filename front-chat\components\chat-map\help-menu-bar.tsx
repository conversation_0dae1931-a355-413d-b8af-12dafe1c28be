import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  MapIcon,
  LayersIcon,
  SettingsIcon,
  FilterIcon,
  HelpCircle,
  ChevronRightIcon,
} from "lucide-react";

interface ExampleCategory {
  title: string;
  icon: React.ReactNode;
  examples: Array<{
    label: string;
    command: string;
  }>;
}

interface HelpMenuBarProps {
  setInput: (input: string) => void;
  hasMessages: boolean;
}

export const HelpMenuBar = ({ setInput, hasMessages }: HelpMenuBarProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const examples: ExampleCategory[] = [
    {
      title: "장소 검색 및 이동",
      icon: <MapIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "위치 이동", command: "웨이버스로 이동해줘" },
        { label: "길찾기", command: "웨이버스에서 평촌역까지 얼마나 걸려?" },
        { label: "현재위치에서 길찾기", command: "내 위치에서 구로디지털단지역까지 얼마나걸려?" },
      ]
    },
    {
      title: "지도 제어",
      icon: <SettingsIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "지도 확대", command: "지도를 확대해줘" },
        { label: "지도 축소", command: "지도를 축소해줘" },
        { label: "위쪽으로 500m 이동", command: "위쪽으로 500m 이동해줘" },
        { label: "항공지도로 변경", command: "배경지도를 항공지도로 변경해줘" }
      ]
    },
    {
      title: "레이어 제어",
      icon: <LayersIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "레이어 추가", command: "택지개발사업 레이어를 추가해줘" },
        { label: "단일 스타일 설정", command: "백년가게를 노란색 별모양으로 보여줄래?" },
        { label: "유형별 스타일 설정", command: "서울에 있는 약국만 빨간색으로 표시해줘" },
        { label: "유형별 스타일 설정", command: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?" },
      ]
    },
    {
      title: "데이터 분석",
      icon: <FilterIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "노후화 건물 분석", command: "서울의 노후화된 건물을 보여줘" },
        { label: "레이어 밀도 분석", command: "AI 발생농가 지역의 밀집도를 분석해줘" },
      ]
    },
  ];

  const handleExampleClick = (command: string) => {
    setInput(command);
    setIsOpen(false);
  };

  // 메시지가 없을 때는 표시하지 않음 (Overview가 표시되므로)
  if (!hasMessages) {
    return null;
  }

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-border/50 bg-secondary/20">
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">💡 도움이 필요하신가요?</span>
      </div>
      
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 px-2 text-xs gap-1 hover:bg-primary/10"
          >
            <HelpCircle className="w-3 h-3" />
            기능 둘러보기
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className="w-80 p-0 bg-gradient-to-b from-background/95 to-background/98 backdrop-blur-lg border border-border/40 shadow-lg"
          align="end"
        >
          <div className="p-4 space-y-3">
            {/* 헤더 */}
            <div className="border-b border-border/40 pb-3">
              <h4 className="font-semibold text-sm bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                사용 가능한 기능들
              </h4>
              <p className="text-xs text-muted-foreground/80 mt-1">
                아래 예시를 클릭하면 자동으로 입력됩니다
              </p>
            </div>

            {/* 카테고리별 예시 */}
            <div className="space-y-3 max-h-80 overflow-y-auto styled-scrollbar">
              {examples.map((category, categoryIndex) => (
                <div key={categoryIndex} className="space-y-2">
                  <div className="flex items-center gap-2">
                    {category.icon}
                    <span className="text-xs font-medium text-foreground/90">
                      {category.title}
                    </span>
                  </div>
                  <div className="space-y-1 ml-6">
                    {category.examples.map((example, exampleIndex) => (
                      <Button
                        key={exampleIndex}
                        variant="ghost"
                        className="w-full group justify-between relative overflow-hidden rounded-md px-2 py-1 h-auto"
                        onClick={() => handleExampleClick(example.command)}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                        <div className="relative flex flex-col items-start gap-0.5 text-left">
                          <span className="text-xs font-medium text-foreground/90 group-hover:text-primary transition-colors">
                            {example.label}
                          </span>
                          <span className="text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors line-clamp-1">
                            {example.command}
                          </span>
                        </div>
                        <div className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <ChevronRightIcon className="w-3 h-3 text-primary" />
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
